PUBLIC FOLDER STRUCTURE
========================

This document outlines the structure of the public folder in the portfolio project.

ROOT LEVEL FILES
================
├── backblue.gif                    # Background image asset
├── cookies.txt                     # Cookie-related configuration
├── fade.gif                        # Fade animation asset
├── favicon.ico                     # Website favicon
├── hts-log.txt                     # HTTrack log file
├── index.html                      # Main entry point
├── index.php                       # PHP entry point
└── robots.txt                      # Search engine crawler instructions

BUILD DIRECTORY
===============
build/
├── assets/                         # Compiled/optimized assets
└── manifest.json                   # Build manifest file

EXTERNAL SITE MIRRORS
=====================
pinterest.com/
└── pin/                           # Pinterest pin pages

s.pinimg.com/
└── webapp/                        # Pinterest webapp assets

www.pinterest.com/
└── pin/                           # Pinterest pin pages (www subdomain)

MAIN SITE CONTENT (zelio.botble.com)
====================================
zelio.botble.com/
├── ajax/                          # AJAX endpoints
├── ar/                            # Arabic language content
├── ar.html                        # Arabic homepage
├── cdn-cgi/                       # CDN configuration
├── feed/                          # RSS/feed content
├── projects/                      # Project showcase
├── services/                      # Services pages
├── storage/                       # File storage
├── tag/                           # Tag-based content
├── themes/                        # Theme assets
├── vendor/                        # Third-party libraries
├── vi/                            # Vietnamese language content
├── vi.html                        # Vietnamese homepage
│
├── MAIN PAGES
│   ├── index.html                 # Homepage
│   ├── blog.html                  # Blog listing
│   ├── blog2679.html              # Blog page variant
│   ├── blog4658.html              # Blog page variant
│   ├── career-journey.html        # Career information
│   ├── collaborations.html        # Collaboration details
│   ├── contact.html               # Contact page
│   ├── cookie-policy.html         # Cookie policy
│   ├── design-portfolio.html      # Design showcase
│   ├── open-source-contributions.html # Open source work
│   ├── personal-blog.html         # Personal blog
│   ├── pricing.html               # Pricing information
│   ├── projects.html              # Projects overview
│   ├── services.html              # Services offered
│   ├── technology-reviews.html    # Tech reviews
│   ├── tutorials.html             # Tutorial content
│   ├── web-development.html       # Web dev content
│   ├── liên hệ.html              # Contact (Vietnamese)
│   ├── liên hệ.z                 # Contact (Vietnamese, compressed)
│   ├── اتصال.html                 # Contact (Arabic)
│   └── اتصال.z                   # Contact (Arabic, compressed)
│
└── BLOG ARTICLES
    ├── 5-essential-tools-for-web-developers-in-2024.html
    ├── a-deep-dive-into-laravel-for-beginners.html
    ├── adapting-to-the-new-web-development-trends-in-2024.html
    ├── best-practices-for-designing-user-friendly-websites.html
    ├── building-a-full-stack-app-with-the-tall-stack.html
    ├── creating-responsive-uis-with-tailwind-css.html
    ├── exploring-the-benefits-of-mysql-for-large-scale-projects.html
    ├── how-i-built-my-personal-portfolio-using-botble-cms.html
    ├── how-to-contribute-to-open-source-a-beginners-guide.html
    ├── how-to-integrate-apis-in-nodejs-for-your-next-project.html
    ├── lessons-from-my-first-web-development-job.html
    ├── my-journey-in-open-source-3-years-of-contributions.html
    ├── my-top-5-github-projects.html
    ├── optimizing-web-performance-with-reactjs.html
    └── why-i-love-contributing-to-open-source-projects.html

NOTES
=====
- The structure appears to be a static site mirror/backup
- Multiple language support (English, Arabic, Vietnamese)
- Contains both original content and external site mirrors
- Build directory suggests a modern build process
- Blog articles focus on web development and open source topics
- Some files have .z extensions (likely compressed versions)

LAST UPDATED: 2025-07-25
