{const mapNumber=(X,A,B,C,D)=>((X-A)*(D-C))/(B-A)+C;const getMousePos=(e)=>{let posx=0;let posy=0;if(!e)e=window.event;if(e.pageX||e.pageY){posx=e.pageX;posy=e.pageY}else if(e.clientX||e.clientY){posx=e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft;posy=e.clientY+document.body.scrollTop+document.documentElement.scrollTop}
    return{x:posx,y:posy}};const getRandomFloat=(min,max)=>(Math.random()*(max-min)+min).toFixed(2);class HoverImgFx1{constructor(el){this.DOM={el:el};this.DOM.reveal=document.createElement('div');this.DOM.reveal.className='tg-img-reveal-wrapper';this.DOM.reveal.innerHTML=`<div class="tg-img-reveal-wrapper__inner">
                <div class="tg-img-reveal-wrapper__img" style="background-image:url(${this.DOM.el.dataset.img})">
                    <div class="tg-hover-wrapper" >
                        <ul class="tgbanner__content-meta list-wrap">
                            <li><span class="by">By</span> <a href="#">${this.DOM.el.dataset.author ? this.DOM.el.dataset.author : ''}</a></li>
                            <li>${this.DOM.el.dataset.date ? this.DOM.el.dataset.date : ''}</li>
                        </ul>
                        <h3 class="tg-hover-title">${this.DOM.el.dataset.title ? this.DOM.el.dataset.title : ''}</h3>
                    </div>
                </div>
            </div>`;this.DOM.el.appendChild(this.DOM.reveal);this.DOM.revealInner=this.DOM.reveal.querySelector('.tg-img-reveal-wrapper__inner');this.DOM.revealInner.style.overflow='hidden';this.DOM.revealImg=this.DOM.revealInner.querySelector('.tg-img-reveal-wrapper__img');this.initEvents()}
    initEvents(){this.positionElement=(ev)=>{const mousePos=getMousePos(ev);const docScrolls={left:document.body.scrollLeft+document.documentElement.scrollLeft,top:document.body.scrollTop+document.documentElement.scrollTop,};this.DOM.reveal.style.top=`${mousePos.y + 20 - docScrolls.top}px`;this.DOM.reveal.style.left=`${mousePos.x + 20 - docScrolls.left}px`};this.mouseenterFn=(ev)=>{this.positionElement(ev);this.showImage()};this.mousemoveFn=(ev)=>requestAnimationFrame(()=>{this.positionElement(ev)});this.mouseleaveFn=()=>{this.hideImage()};this.DOM.el.addEventListener('mouseenter',this.mouseenterFn);this.DOM.el.addEventListener('mousemove',this.mousemoveFn);this.DOM.el.addEventListener('mouseleave',this.mouseleaveFn)}
    showImage(){TweenMax.killTweensOf(this.DOM.revealInner);TweenMax.killTweensOf(this.DOM.revealImg);this.tl=new TimelineMax({onStart:()=>{this.DOM.reveal.style.opacity=1;TweenMax.set(this.DOM.el,{zIndex:9})},}).add('begin').add(new TweenMax(this.DOM.revealInner,0.2,{ease:Sine.easeOut,startAt:{x:'-100%'},x:'0%',}),'begin',).add(new TweenMax(this.DOM.revealImg,0.2,{ease:Sine.easeOut,startAt:{x:'100%'},x:'0%',}),'begin',)}
    hideImage(){TweenMax.killTweensOf(this.DOM.revealInner);TweenMax.killTweensOf(this.DOM.revealImg);this.tl=new TimelineMax({onStart:()=>{TweenMax.set(this.DOM.el,{zIndex:8})},onComplete:()=>{TweenMax.set(this.DOM.el,{zIndex:''});TweenMax.set(this.DOM.reveal,{opacity:0})},}).add('begin').add(new TweenMax(this.DOM.revealInner,0.2,{ease:Sine.easeOut,x:'100%',}),'begin',).add(new TweenMax(this.DOM.revealImg,0.2,{ease:Sine.easeOut,x:'-100%',}),'begin',)}}
    function initImageRevealHover(){[...document.querySelectorAll('[data-fx="pt1"] > .tg-img-reveal-item, .tg-img-reveal-item[data-fx="pt1"]')].forEach((link)=>new HoverPTCard1(link));[...document.querySelectorAll('[data-fx="1"] > .tg-img-reveal-item, .tg-img-reveal-item[data-fx="1"]')].forEach((link)=>new HoverImgFx1(link));const contentel=document.querySelector('.content');[...document.querySelectorAll('.block__title, .block__link, .content__text-link')].forEach((el)=>{const imgsArr=el.dataset.img.split(',');el.dataset.subtitle.split(',');el.dataset.title.split(',');el.dataset.metadate.split(',');el.dataset.metaauthor.split(',');for(let i=0,len=imgsArr.length;i<=len-1;++i){const imgel=document.createElement('img');imgel.style.visibility='hidden';imgel.style.width=0;imgel.src=imgsArr[i];imgel.className='preload';contentel.appendChild(imgel)}})}
    initImageRevealHover()}
