/*!
 * ScrollTrigger 3.12.0
 * https://greensock.com
 *
 * @license Copyright 2023, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(){return Se||"undefined"!=typeof window&&(Se=window.gsap)&&Se.registerPlugin&&Se}function z(e,t){return~Fe.indexOf(e)&&Fe[Fe.indexOf(e)+1][t]}function A(e){return!!~t.indexOf(e)}function B(e,t,r,n,i){return e.addEventListener(t,r,{passive:!n,capture:!!i})}function C(e,t,r,n){return e.removeEventListener(t,r,!!n)}function F(){return De&&De.isPressed||ze.cache++}function G(r,n){function Yc(e){if(e||0===e){i&&(ke.history.scrollRestoration="manual");var t=De&&De.isPressed;e=Yc.v=Math.round(e)||(De&&De.iOS?1:0),r(e),Yc.cacheID=ze.cache,t&&o("ss",e)}else(n||ze.cache!==Yc.cacheID||o("ref"))&&(Yc.cacheID=ze.cache,Yc.v=r());return Yc.v+Yc.offset}return Yc.offset=0,r&&Yc}function J(e,t){return(t&&t._ctx&&t._ctx.selector||Se.utils.toArray)(e)[0]||("string"==typeof e&&!1!==Se.config().nullTargetWarn?console.warn("Element not found:",e):null)}function K(t,e){var r=e.s,n=e.sc;A(t)&&(t=Te.scrollingElement||Ee);var i=ze.indexOf(t),o=n===Ie.sc?1:2;~i||(i=ze.push(t)-1),ze[i+o]||t.addEventListener("scroll",F);var a=ze[i+o],s=a||(ze[i+o]=G(z(t,r),!0)||(A(t)?n:G(function(e){return arguments.length?t[r]=e:t[r]})));return s.target=t,a||(s.smooth="smooth"===Se.getProperty(t,"scrollBehavior")),s}function L(e,t,i){function vd(e,t){var r=qe();t||n<r-s?(a=o,o=e,l=s,s=r):i?o+=e:o=a+(e-a)/(r-l)*(s-l)}var o=e,a=e,s=qe(),l=s,n=t||50,c=Math.max(500,3*n);return{update:vd,reset:function reset(){a=o=i?0:o,l=s=0},getVelocity:function getVelocity(e){var t=l,r=a,n=qe();return!e&&0!==e||e===o||vd(e),s===l||c<n-l?0:(o+(i?r:-r))/((i?n:s)-t)*1e3}}}function M(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function N(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r}function O(){(Ae=Se.core.globals().ScrollTrigger)&&Ae.core&&function _integrate(){var e=Ae.core,r=e.bridge||{},t=e._scrollers,n=e._proxies;t.push.apply(t,ze),n.push.apply(n,Fe),ze=t,Fe=n,o=function _bridge(e,t){return r[e](t)}}()}function P(e){return(Se=e||r())&&"undefined"!=typeof document&&document.body&&(ke=window,Ee=(Te=document).documentElement,Pe=Te.body,t=[ke,Te,Ee,Pe],Se.utils.clamp,Be=Se.core.context||function(){},Oe="onpointerenter"in Pe?"pointer":"mouse",Me=T.isTouch=ke.matchMedia&&ke.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in ke||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,Re=T.eventTypes=("ontouchstart"in Ee?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Ee?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return i=0},500),O(),Ce=1),Ce}var Se,Ce,ke,Te,Ee,Pe,Me,Oe,Ae,t,De,Re,Be,i=1,Ye=[],ze=[],Fe=[],qe=Date.now,o=function _bridge(e,t){return t},n="scrollLeft",a="scrollTop",Le={s:n,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:G(function(e){return arguments.length?ke.scrollTo(e,Ie.sc()):ke.pageXOffset||Te[n]||Ee[n]||Pe[n]||0})},Ie={s:a,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Le,sc:G(function(e){return arguments.length?ke.scrollTo(Le.sc(),e):ke.pageYOffset||Te[a]||Ee[a]||Pe[a]||0})};Le.op=Ie,ze.cache=0;var T=(Observer.prototype.init=function init(e){Ce||P(Se)||console.warn("Please gsap.registerPlugin(Observer)"),Ae||O();var i=e.tolerance,a=e.dragMinimum,t=e.type,o=e.target,r=e.lineHeight,n=e.debounce,s=e.preventDefault,l=e.onStop,c=e.onStopDelay,u=e.ignore,f=e.wheelSpeed,p=e.event,d=e.onDragStart,h=e.onDragEnd,g=e.onDrag,v=e.onPress,b=e.onRelease,m=e.onRight,y=e.onLeft,x=e.onUp,_=e.onDown,w=e.onChangeX,S=e.onChangeY,k=e.onChange,T=e.onToggleX,E=e.onToggleY,D=e.onHover,R=e.onHoverEnd,Y=e.onMove,z=e.ignoreCheck,q=e.isNormalizer,I=e.onGestureStart,X=e.onGestureEnd,H=e.onWheel,W=e.onEnable,G=e.onDisable,j=e.onClick,V=e.scrollSpeed,U=e.capture,Q=e.allowClicks,Z=e.lockAxis,$=e.onLockAxis;function We(){return ye=qe()}function Xe(e,t){return(se.event=e)&&u&&~u.indexOf(e.target)||t&&he&&"touch"!==e.pointerType||z&&z(e,t)}function Ze(){var e=se.deltaX=N(be),t=se.deltaY=N(me),r=Math.abs(e)>=i,n=Math.abs(t)>=i;k&&(r||n)&&k(se,e,t,be,me),r&&(m&&0<se.deltaX&&m(se),y&&se.deltaX<0&&y(se),w&&w(se),T&&se.deltaX<0!=le<0&&T(se),le=se.deltaX,be[0]=be[1]=be[2]=0),n&&(_&&0<se.deltaY&&_(se),x&&se.deltaY<0&&x(se),S&&S(se),E&&se.deltaY<0!=ce<0&&E(se),ce=se.deltaY,me[0]=me[1]=me[2]=0),(ne||re)&&(Y&&Y(se),re&&(g(se),re=!1),ne=!1),oe&&!(oe=!1)&&$&&$(se),ie&&(H(se),ie=!1),ee=0}function $e(e,t,r){be[r]+=e,me[r]+=t,se._vx.update(e),se._vy.update(t),n?ee=ee||requestAnimationFrame(Ze):Ze()}function _e(e,t){Z&&!ae&&(se.axis=ae=Math.abs(e)>Math.abs(t)?"x":"y",oe=!0),"y"!==ae&&(be[2]+=e,se._vx.update(e,!0)),"x"!==ae&&(me[2]+=t,se._vy.update(t,!0)),n?ee=ee||requestAnimationFrame(Ze):Ze()}function af(e){if(!Xe(e,1)){var t=(e=M(e,s)).clientX,r=e.clientY,n=t-se.x,i=r-se.y,o=se.isDragging;se.x=t,se.y=r,(o||Math.abs(se.startX-t)>=a||Math.abs(se.startY-r)>=a)&&(g&&(re=!0),o||(se.isDragging=!0),_e(n,i),o||d&&d(se))}}function df(e){return e.touches&&1<e.touches.length&&(se.isGesturing=!0)&&I(e,se.isDragging)}function ef(){return(se.isGesturing=!1)||X(se)}function ff(e){if(!Xe(e)){var t=ue(),r=fe();$e((t-pe)*V,(r-de)*V,1),pe=t,de=r,l&&te.restart(!0)}}function gf(e){if(!Xe(e)){e=M(e,s),H&&(ie=!0);var t=(1===e.deltaMode?r:2===e.deltaMode?ke.innerHeight:1)*f;$e(e.deltaX*t,e.deltaY*t,0),l&&!q&&te.restart(!0)}}function hf(e){if(!Xe(e)){var t=e.clientX,r=e.clientY,n=t-se.x,i=r-se.y;se.x=t,se.y=r,ne=!0,(n||i)&&_e(n,i)}}function jf(e){se.event=e,D(se)}function kf(e){se.event=e,R(se)}function lf(e){return Xe(e)||M(e,s)&&j(se)}this.target=o=J(o)||Ee,this.vars=e,u=u&&Se.utils.toArray(u),i=i||1e-9,a=a||0,f=f||1,V=V||1,t=t||"wheel,touch,pointer",n=!1!==n,r=r||parseFloat(ke.getComputedStyle(Pe).lineHeight)||22;var ee,te,re,ne,ie,oe,ae,se=this,le=0,ce=0,ue=K(o,Le),fe=K(o,Ie),pe=ue(),de=fe(),he=~t.indexOf("touch")&&!~t.indexOf("pointer")&&"pointerdown"===Re[0],ge=A(o),ve=o.ownerDocument||Te,be=[0,0,0],me=[0,0,0],ye=0,xe=se.onPress=function(e){Xe(e,1)||e&&e.button||(se.axis=ae=null,te.pause(),se.isPressed=!0,e=M(e),le=ce=0,se.startX=se.x=e.clientX,se.startY=se.y=e.clientY,se._vx.reset(),se._vy.reset(),B(q?o:ve,Re[1],af,s,!0),se.deltaX=se.deltaY=0,v&&v(se))},we=se.onRelease=function(t){if(!Xe(t,1)){C(q?o:ve,Re[1],af,!0);var e=!isNaN(se.y-se.startY),r=se.isDragging&&(3<Math.abs(se.x-se.startX)||3<Math.abs(se.y-se.startY)),n=M(t);!r&&e&&(se._vx.reset(),se._vy.reset(),s&&Q&&Se.delayedCall(.08,function(){if(300<qe()-ye&&!t.defaultPrevented)if(t.target.click)t.target.click();else if(ve.createEvent){var e=ve.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,ke,1,n.screenX,n.screenY,n.clientX,n.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}})),se.isDragging=se.isGesturing=se.isPressed=!1,l&&!q&&te.restart(!0),h&&r&&h(se),b&&b(se,r)}};te=se._dc=Se.delayedCall(c||.25,function onStopFunc(){se._vx.reset(),se._vy.reset(),te.pause(),l&&l(se)}).pause(),se.deltaX=se.deltaY=0,se._vx=L(0,50,!0),se._vy=L(0,50,!0),se.scrollX=ue,se.scrollY=fe,se.isDragging=se.isGesturing=se.isPressed=!1,Be(this),se.enable=function(e){return se.isEnabled||(B(ge?ve:o,"scroll",F),0<=t.indexOf("scroll")&&B(ge?ve:o,"scroll",ff,s,U),0<=t.indexOf("wheel")&&B(o,"wheel",gf,s,U),(0<=t.indexOf("touch")&&Me||0<=t.indexOf("pointer"))&&(B(o,Re[0],xe,s,U),B(ve,Re[2],we),B(ve,Re[3],we),Q&&B(o,"click",We,!1,!0),j&&B(o,"click",lf),I&&B(ve,"gesturestart",df),X&&B(ve,"gestureend",ef),D&&B(o,Oe+"enter",jf),R&&B(o,Oe+"leave",kf),Y&&B(o,Oe+"move",hf)),se.isEnabled=!0,e&&e.type&&xe(e),W&&W(se)),se},se.disable=function(){se.isEnabled&&(Ye.filter(function(e){return e!==se&&A(e.target)}).length||C(ge?ve:o,"scroll",F),se.isPressed&&(se._vx.reset(),se._vy.reset(),C(q?o:ve,Re[1],af,!0)),C(ge?ve:o,"scroll",ff,U),C(o,"wheel",gf,U),C(o,Re[0],xe,U),C(ve,Re[2],we),C(ve,Re[3],we),C(o,"click",We,!0),C(o,"click",lf),C(ve,"gesturestart",df),C(ve,"gestureend",ef),C(o,Oe+"enter",jf),C(o,Oe+"leave",kf),C(o,Oe+"move",hf),se.isEnabled=se.isPressed=se.isDragging=!1,G&&G(se))},se.kill=se.revert=function(){se.disable();var e=Ye.indexOf(se);0<=e&&Ye.splice(e,1),De===se&&(De=0)},Ye.push(se),q&&A(o)&&(De=se),se.enable(p)},function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(Observer,[{key:"velocityX",get:function get(){return this._vx.getVelocity()}},{key:"velocityY",get:function get(){return this._vy.getVelocity()}}]),Observer);function Observer(e){this.init(e)}T.version="3.12.0",T.create=function(e){return new T(e)},T.register=P,T.getAll=function(){return Ye.slice()},T.getById=function(t){return Ye.filter(function(e){return e.vars.id===t})[0]},r()&&Se.registerPlugin(T);function ya(e,t,r){var n=pt(e)&&("clamp("===e.substr(0,6)||-1<e.indexOf("max"));return(r["_"+t+"Clamp"]=n)?e.substr(6,e.length-7):e}function za(e,t){return!t||pt(e)&&"clamp("===e.substr(0,6)?e:"clamp("+e+")"}function Ba(){return et=1}function Ca(){return et=0}function Da(e){return e}function Ea(e){return Math.round(1e5*e)/1e5||0}function Fa(){return"undefined"!=typeof window}function Ga(){return Ne||Fa()&&(Ne=window.gsap)&&Ne.registerPlugin&&Ne}function Ha(e){return!!~l.indexOf(e)}function Ia(e){return z(e,"getBoundingClientRect")||(Ha(e)?function(){return Rt.width=He.innerWidth,Rt.height=He.innerHeight,Rt}:function(){return Ct(e)})}function La(e,t){var r=t.s,n=t.d2,i=t.d,o=t.a;return Math.max(0,(r="scroll"+n)&&(o=z(e,r))?o()-Ia(e)()[i]:Ha(e)?(je[r]||Je[r])-(He["inner"+n]||je["client"+n]||Je["client"+n]):e[r]-e["offset"+n])}function Ma(e,t){for(var r=0;r<h.length;r+=3)t&&!~t.indexOf(h[r+1])||e(h[r],h[r+1],h[r+2])}function Oa(e){return"function"==typeof e}function Pa(e){return"number"==typeof e}function Qa(e){return"object"==typeof e}function Ra(e,t,r){return e&&e.progress(t?0:1)&&r&&e.pause()}function Sa(e,t){if(e.enabled){var r=t(e);r&&r.totalTime&&(e.callbackAnimation=r)}}function hb(e){return He.getComputedStyle(e)}function jb(e,t){for(var r in t)r in e||(e[r]=t[r]);return e}function lb(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0}function mb(e){var t,r=[],n=e.labels,i=e.duration();for(t in n)r.push(n[t]/i);return r}function ob(i){var o=Ne.utils.snap(i),a=Array.isArray(i)&&i.slice(0).sort(function(e,t){return e-t});return a?function(e,t,r){var n;if(void 0===r&&(r=.001),!t)return o(e);if(0<t){for(e-=r,n=0;n<a.length;n++)if(a[n]>=e)return a[n];return a[n-1]}for(n=a.length,e+=r;n--;)if(a[n]<=e)return a[n];return a[0]}:function(e,t,r){void 0===r&&(r=.001);var n=o(e);return!t||Math.abs(n-e)<r||n-e<0==t<0?n:o(t<0?e-i:e+i)}}function qb(t,r,e,n){return e.split(",").forEach(function(e){return t(r,e,n)})}function rb(e,t,r,n,i){return e.addEventListener(t,r,{passive:!n,capture:!!i})}function sb(e,t,r,n){return e.removeEventListener(t,r,!!n)}function tb(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))}function xb(e,t){if(pt(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in Y?Y[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function yb(e,t,r,n,i,o,a,s){var l=i.startColor,c=i.endColor,u=i.fontSize,f=i.indent,p=i.fontWeight,d=Ge.createElement("div"),h=Ha(r)||"fixed"===z(r,"pinType"),g=-1!==e.indexOf("scroller"),v=h?Je:r,b=-1!==e.indexOf("start"),m=b?l:c,y="border-color:"+m+";font-size:"+u+";color:"+m+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((g||s)&&h?"fixed;":"absolute;"),!g&&!s&&h||(y+=(n===Ie?k:D)+":"+(o+parseFloat(f))+"px;"),a&&(y+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),d._isStart=b,d.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),d.style.cssText=y,d.innerText=t||0===t?e+"-"+t:e,v.children[0]?v.insertBefore(d,v.children[0]):v.appendChild(d),d._offset=d["offset"+n.op.d2],q(d,0,n,b),d}function Db(){return 34<ct()-ut&&(w=w||requestAnimationFrame(V))}function Eb(){v&&v.isPressed&&!(v.startX>Je.clientWidth)||(ze.cache++,v?w=w||requestAnimationFrame(V):V(),ut||H("scrollStart"),ut=ct())}function Fb(){y=He.innerWidth,m=He.innerHeight}function Gb(){ze.cache++,Qe||g||Ge.fullscreenElement||Ge.webkitFullscreenElement||b&&y===He.innerWidth&&!(Math.abs(He.innerHeight-m)>.25*He.innerHeight)||c.restart(!0)}function Jb(){return sb(ee,"scrollEnd",Jb)||Ot(!0)}function Mb(e){for(var t=0;t<W.length;t+=5)(!e||W[t+4]&&W[t+4].query===e)&&(W[t].style.cssText=W[t+1],W[t].getBBox&&W[t].setAttribute("transform",W[t+2]||""),W[t+3].uncache=1)}function Nb(e,t){var r;for(tt=0;tt<Et.length;tt++)!(r=Et[tt])||t&&r._ctx!==t||(e?r.kill(1):r.revert(!0,!0));t&&Mb(t),t||H("revert")}function Ob(e,t){ze.cache++,!t&&ot||ze.forEach(function(e){return Oa(e)&&e.cacheID++&&(e.rec=0)}),pt(e)&&(He.history.scrollRestoration=_=e)}function _b(e,t,r,n){if(!e._gsap.swappedIn){for(var i,o=U.length,a=t.style,s=e.style;o--;)a[i=U[o]]=r[i];a.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(a.display="inline-block"),s[D]=s[k]="auto",a.flexBasis=r.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[ht]=lb(e,Le)+St,a[gt]=lb(e,Ie)+St,a[xt]=s[_t]=s.top=s.left="0",Dt(n),s[ht]=s.maxWidth=r[ht],s[gt]=s.maxHeight=r[gt],s[xt]=r[xt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function cc(e){for(var t=Q.length,r=e.style,n=[],i=0;i<t;i++)n.push(Q[i],r[Q[i]]);return n.t=e,n}function fc(e,t,r,n,i,o,a,s,l,c,u,f,p,d){Oa(e)&&(e=e(s)),pt(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?xb("0"+e.substr(3),r):0));var h,g,v,b=p?p.time():0;if(p&&p.seek(0),isNaN(e)||(e=+e),Pa(e))p&&(e=Ne.utils.mapRange(p.scrollTrigger.start,p.scrollTrigger.end,0,f,e)),a&&q(a,r,n,!0);else{Oa(t)&&(t=t(s));var m,y,x,_,w=(e||"0").split(" ");v=J(t,s)||Je,(m=Ct(v)||{})&&(m.left||m.top)||"none"!==hb(v).display||(_=v.style.display,v.style.display="block",m=Ct(v),_?v.style.display=_:v.style.removeProperty("display")),y=xb(w[0],m[n.d]),x=xb(w[1]||"0",r),e=m[n.p]-l[n.p]-c+y+i-x,a&&q(a,x,n,r-x<20||a._isStart&&20<x),r-=r-x}if(d&&(s[d]=e||-.001,e<0&&(e=0)),o){var S=e+r,C=o._isStart;h="scroll"+n.d2,q(o,S,n,C&&20<S||!C&&(u?Math.max(Je[h],je[h]):o.parentNode[h])<=S+1),u&&(l=Ct(a),u&&(o.style[n.op.p]=l[n.op.p]-n.op.m-o._offset+St))}return p&&v&&(h=Ct(v),p.seek(f),g=Ct(v),p._caScrollDist=h[n.p]-g[n.p],e=e/p._caScrollDist*f),p&&p.seek(b),p?e:Math.round(e)}function hc(e,t,r,n){if(e.parentNode!==t){var i,o,a=e.style;if(t===Je){for(i in e._stOrig=a.cssText,o=hb(e))+i||$.test(i)||!o[i]||"string"!=typeof a[i]||"0"===i||(a[i]=o[i]);a.top=r,a.left=n}else a.cssText=e._stOrig;Ne.core.getCache(e).uncache=1,t.appendChild(e)}}function ic(r,e,n){var i=e,o=i;return function(e){var t=Math.round(r());return t!==i&&t!==o&&3<Math.abs(t-i)&&3<Math.abs(t-o)&&(e=t,n&&n()),o=i,i=e}}function jc(e,t,r){var n={};n[t.p]="+="+r,Ne.set(e,n)}function kc(c,e){function pk(e,t,r,n,i){var o=pk.tween,a=t.onComplete,s={};r=r||u();var l=ic(u,r,function(){o.kill(),pk.tween=0});return i=n&&i||0,n=n||e-r,o&&o.kill(),t[f]=e,(t.modifiers=s)[f]=function(){return l(r+n*o.ratio+i*o.ratio*o.ratio)},t.onUpdate=function(){ze.cache++,V()},t.onComplete=function(){pk.tween=0,a&&a.call(o)},o=pk.tween=Ne.to(c,t)}var u=K(c,e),f="_scroll"+e.p2;return(c[f]=u).wheelHandler=function(){return pk.tween&&pk.tween.kill()&&(pk.tween=0)},rb(c,"wheel",u.wheelHandler),ee.isTouch&&rb(c,"touchmove",u.wheelHandler),pk}var Ne,s,He,Ge,je,Je,l,c,Ve,Ue,Ke,u,Qe,et,f,tt,p,d,h,rt,nt,g,v,b,m,y,E,x,_,it,w,ot,at,st,lt=1,ct=Date.now,S=ct(),ut=0,ft=0,pt=function _isString(e){return"string"==typeof e},dt=Math.abs,k="right",D="bottom",ht="width",gt="height",vt="Right",bt="Left",mt="Top",yt="Bottom",xt="padding",_t="margin",wt="Width",R="Height",St="px",Ct=function _getBounds(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==hb(e)[f]&&Ne.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},kt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Tt={toggleActions:"play",anticipatePin:0},Y={top:0,left:0,center:.5,bottom:1,right:1},q=function _positionMarker(e,t,r,n){var i={display:"block"},o=r[n?"os2":"p2"],a=r[n?"p2":"os2"];e._isFlipped=n,i[r.a+"Percent"]=n?-100:0,i[r.a]=n?"1px":0,i["border"+o+wt]=1,i["border"+a+wt]=0,i[r.p]=t+"px",Ne.set(e,i)},Et=[],Pt={},I={},X=[],H=function _dispatch(e){return I[e]&&I[e].map(function(e){return e()})||X},W=[],Mt=0,Ot=function _refreshAll(e,t){if(!ut||e){ot=ee.isRefreshing=!0,ze.forEach(function(e){return Oa(e)&&++e.cacheID&&(e.rec=e())});var r=H("refreshInit");rt&&ee.sort(),t||Nb(),ze.forEach(function(e){Oa(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),Et.slice(0).forEach(function(e){return e.refresh()}),Et.forEach(function(e,t){if(e._subPinOffset&&e.pin){var r=e.vars.horizontal?"offsetWidth":"offsetHeight",n=e.pin[r];e.revert(!0,1),e.adjustPinSpacing(e.pin[r]-n),e.refresh()}}),Et.forEach(function(e){var t=La(e.scroller,e._dir);("max"===e.vars.end||e._endClamp&&e.end>t)&&e.setPositions(e.start,Math.max(e.start+1,t),!0)}),r.forEach(function(e){return e&&e.render&&e.render(-1)}),ze.forEach(function(e){Oa(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),Ob(_,1),c.pause(),Mt++,V(ot=2),Et.forEach(function(e){return Oa(e.vars.onRefresh)&&e.vars.onRefresh(e)}),ot=ee.isRefreshing=!1,H("refresh")}else rb(ee,"scrollEnd",Jb)},j=0,At=1,V=function _updateAll(e){if(!ot||2===e){ee.isUpdating=!0,st&&st.update(0);var t=Et.length,r=ct(),n=50<=r-S,i=t&&Et[0].scroll();if(At=i<j?-1:1,ot||(j=i),n&&(ut&&!et&&200<r-ut&&(ut=0,H("scrollEnd")),Ke=S,S=r),At<0){for(tt=t;0<tt--;)Et[tt]&&Et[tt].update(0,n);At=1}else for(tt=0;tt<t;tt++)Et[tt]&&Et[tt].update(0,n);ee.isUpdating=!1}w=0},U=["left","top",D,k,_t+yt,_t+vt,_t+mt,_t+bt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Q=U.concat([ht,gt,"boxSizing","max"+wt,"max"+R,"position",_t,xt,xt+mt,xt+vt,xt+yt,xt+bt]),Z=/([A-Z])/g,Dt=function _setState(e){if(e){var t,r,n=e.t.style,i=e.length,o=0;for((e.t._gsap||Ne.core.getCache(e.t)).uncache=1;o<i;o+=2)r=e[o+1],t=e[o],r?n[t]=r:n[t]&&n.removeProperty(t.replace(Z,"-$1").toLowerCase())}},Rt={left:0,top:0},$=/(webkit|moz|length|cssText|inset)/i,ee=(ScrollTrigger.prototype.init=function init(M,O){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),ft){var A,n,d,D,R,B,Y,F,q,L,I,e,X,N,H,W,G,j,t,V,b,U,Q,m,Z,y,$,x,r,_,w,ee,i,h,te,re,ne,S,o,C=(M=jb(pt(M)||Pa(M)||M.nodeType?{trigger:M}:M,Tt)).onUpdate,k=M.toggleClass,a=M.id,T=M.onToggle,ie=M.onRefresh,E=M.scrub,oe=M.trigger,ae=M.pin,se=M.pinSpacing,le=M.invalidateOnRefresh,P=M.anticipatePin,s=M.onScrubComplete,g=M.onSnapComplete,ce=M.once,ue=M.snap,fe=M.pinReparent,l=M.pinSpacer,pe=M.containerAnimation,de=M.fastScrollEnd,he=M.preventOverlaps,ge=M.horizontal||M.containerAnimation&&!1!==M.horizontal?Le:Ie,ve=!E&&0!==E,be=J(M.scroller||He),c=Ne.core.getCache(be),me=Ha(be),ye="fixed"===("pinType"in M?M.pinType:z(be,"pinType")||me&&"fixed"),xe=[M.onEnter,M.onLeave,M.onEnterBack,M.onLeaveBack],_e=ve&&M.toggleActions.split(" "),we="markers"in M?M.markers:Tt.markers,Se=me?0:parseFloat(hb(be)["border"+ge.p2+wt])||0,Ce=this,ke=M.onRefreshInit&&function(){return M.onRefreshInit(Ce)},Te=function _getSizeFunc(e,t,r){var n=r.d,i=r.d2,o=r.a;return(o=z(e,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(t?He["inner"+i]:e["client"+i])||0}}(be,me,ge),Ee=function _getOffsetsFunc(e,t){return!t||~Fe.indexOf(e)?Ia(e):function(){return Rt}}(be,me),Pe=0,Me=0,Oe=0,Ae=K(be,ge);if(Ce._startClamp=Ce._endClamp=!1,Ce._dir=ge,P*=45,Ce.scroller=be,Ce.scroll=pe?pe.time.bind(pe):Ae,D=Ae(),Ce.vars=M,O=O||M.animation,"refreshPriority"in M&&(rt=1,-9999===M.refreshPriority&&(st=Ce)),c.tweenScroll=c.tweenScroll||{top:kc(be,Ie),left:kc(be,Le)},Ce.tweenTo=A=c.tweenScroll[ge.p],Ce.scrubDuration=function(e){(i=Pa(e)&&e)?ee?ee.duration(e):ee=Ne.to(O,{ease:"expo",totalProgress:"+=0",duration:i,paused:!0,onComplete:function onComplete(){return s&&s(Ce)}}):(ee&&ee.progress(1).kill(),ee=0)},O&&(O.vars.lazy=!1,O._initted&&!Ce.isReverted||!1!==O.vars.immediateRender&&!1!==M.immediateRender&&O.duration()&&O.render(0,!0,!0),Ce.animation=O.pause(),(O.scrollTrigger=Ce).scrubDuration(E),_=0,a=a||O.vars.id),ue&&(Qa(ue)&&!ue.push||(ue={snapTo:ue}),"scrollBehavior"in Je.style&&Ne.set(me?[Je,je]:be,{scrollBehavior:"auto"}),ze.forEach(function(e){return Oa(e)&&e.target===(me?Ge.scrollingElement||je:be)&&(e.smooth=!1)}),d=Oa(ue.snapTo)?ue.snapTo:"labels"===ue.snapTo?function _getClosestLabel(t){return function(e){return Ne.utils.snap(mb(t),e)}}(O):"labelsDirectional"===ue.snapTo?function _getLabelAtDirection(r){return function(e,t){return ob(mb(r))(e,t.direction)}}(O):!1!==ue.directional?function(e,t){return ob(ue.snapTo)(e,ct()-Me<500?0:t.direction)}:Ne.utils.snap(ue.snapTo),h=ue.duration||{min:.1,max:2},h=Qa(h)?Ue(h.min,h.max):Ue(h,h),te=Ne.delayedCall(ue.delay||i/2||.1,function(){var e=Ae(),t=ct()-Me<500,r=A.tween;if(!(t||Math.abs(Ce.getVelocity())<10)||r||et||Pe===e)Ce.isActive&&Pe!==e&&te.restart(!0);else{var n=(e-B)/N,i=O&&!ve?O.totalProgress():n,o=t?0:(i-w)/(ct()-Ke)*1e3||0,a=Ne.utils.clamp(-n,1-n,dt(o/2)*o/.185),s=n+(!1===ue.inertia?0:a),l=Ue(0,1,d(s,Ce)),c=Math.round(B+l*N),u=ue.onStart,f=ue.onInterrupt,p=ue.onComplete;if(e<=Y&&B<=e&&c!==e){if(r&&!r._initted&&r.data<=dt(c-e))return;!1===ue.inertia&&(a=l-n),A(c,{duration:h(dt(.185*Math.max(dt(s-i),dt(l-i))/o/.05||0)),ease:ue.ease||"power3",data:dt(c-e),onInterrupt:function onInterrupt(){return te.restart(!0)&&f&&f(Ce)},onComplete:function onComplete(){Ce.update(),Pe=Ae(),_=w=O&&!ve?O.totalProgress():Ce.progress,g&&g(Ce),p&&p(Ce)}},e,a*N,c-e-a*N),u&&u(Ce,A.tween)}}}).pause()),a&&(Pt[a]=Ce),o=(o=(oe=Ce.trigger=J(oe||!0!==ae&&ae))&&oe._gsap&&oe._gsap.stRevert)&&o(Ce),ae=!0===ae?oe:J(ae),pt(k)&&(k={targets:oe,className:k}),ae&&(!1===se||se===_t||(se=!(!se&&ae.parentNode&&ae.parentNode.style&&"flex"===hb(ae.parentNode).display)&&xt),Ce.pin=ae,(n=Ne.core.getCache(ae)).spacer?H=n.pinState:(l&&((l=J(l))&&!l.nodeType&&(l=l.current||l.nativeElement),n.spacerIsNative=!!l,l&&(n.spacerState=cc(l))),n.spacer=j=l||Ge.createElement("div"),j.classList.add("pin-spacer"),a&&j.classList.add("pin-spacer-"+a),n.pinState=H=cc(ae)),!1!==M.force3D&&Ne.set(ae,{force3D:!0}),Ce.spacer=j=n.spacer,r=hb(ae),m=r[se+ge.os2],V=Ne.getProperty(ae),b=Ne.quickSetter(ae,ge.a,St),_b(ae,j,r),G=cc(ae)),we){e=Qa(we)?jb(we,kt):kt,L=yb("scroller-start",a,be,ge,e,0),I=yb("scroller-end",a,be,ge,e,0,L),t=L["offset"+ge.op.d2];var u=J(z(be,"content")||be);F=this.markerStart=yb("start",a,u,ge,e,t,0,pe),q=this.markerEnd=yb("end",a,u,ge,e,t,0,pe),pe&&(S=Ne.quickSetter([F,q],ge.a,St)),ye||Fe.length&&!0===z(be,"fixedMarkers")||(function _makePositionable(e){var t=hb(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(me?Je:be),Ne.set([L,I],{force3D:!0}),y=Ne.quickSetter(L,ge.a,St),x=Ne.quickSetter(I,ge.a,St))}if(pe){var f=pe.vars.onUpdate,p=pe.vars.onUpdateParams;pe.eventCallback("onUpdate",function(){Ce.update(0,0,1),f&&f.apply(pe,p||[])})}if(Ce.previous=function(){return Et[Et.indexOf(Ce)-1]},Ce.next=function(){return Et[Et.indexOf(Ce)+1]},Ce.revert=function(e,t){if(!t)return Ce.kill(!0);var r=!1!==e||!Ce.enabled,n=Qe;r!==Ce.isReverted&&(r&&(re=Math.max(Ae(),Ce.scroll.rec||0),Oe=Ce.progress,ne=O&&O.progress()),F&&[F,q,L,I].forEach(function(e){return e.style.display=r?"none":"block"}),r&&(Qe=Ce).update(r),!ae||fe&&Ce.isActive||(r?function _swapPinOut(e,t,r){Dt(r);var n=e._gsap;if(n.spacerIsNative)Dt(n.spacerState);else if(e._gsap.swappedIn){var i=t.parentNode;i&&(i.insertBefore(e,t),i.removeChild(t))}e._gsap.swappedIn=!1}(ae,j,H):_b(ae,j,hb(ae),Z)),r||Ce.update(r),Qe=n,Ce.isReverted=r)},Ce.refresh=function(e,t,r,n){if(!Qe&&Ce.enabled||t)if(ae&&e&&ut)rb(ScrollTrigger,"scrollEnd",Jb);else{!ot&&ke&&ke(Ce),Qe=Ce,A.tween&&(A.tween.kill(),A.tween=0),ee&&ee.pause(),le&&O&&O.revert({kill:!1}).invalidate(),Ce.isReverted||Ce.revert(!0,!0),Ce._subPinOffset=!1;var i,o,a,s,l,c,u,f,p,d,h,g,v,b=Te(),m=Ee(),y=pe?pe.duration():La(be,ge),x=N<=.01,_=0,w=n||0,S=Qa(r)?r.end:M.end,C=M.endTrigger||oe,k=Qa(r)?r.start:M.start||(0!==M.start&&oe?ae?"0 0":"0 100%":0),T=Ce.pinnedContainer=M.pinnedContainer&&J(M.pinnedContainer,Ce),E=oe&&Math.max(0,Et.indexOf(Ce))||0,P=E;for(we&&Qa(r)&&(g=Ne.getProperty(L,ge.p),v=Ne.getProperty(I,ge.p));P--;)(c=Et[P]).end||c.refresh(0,1)||(Qe=Ce),!(u=c.pin)||u!==oe&&u!==ae&&u!==T||c.isReverted||((d=d||[]).unshift(c),c.revert(!0,!0)),c!==Et[P]&&(E--,P--);for(Oa(k)&&(k=k(Ce)),k=ya(k,"start",Ce),B=fc(k,oe,b,ge,Ae(),F,L,Ce,m,Se,ye,y,pe,Ce._startClamp&&"_startClamp")||(ae?-.001:0),Oa(S)&&(S=S(Ce)),pt(S)&&!S.indexOf("+=")&&(~S.indexOf(" ")?S=(pt(k)?k.split(" ")[0]:"")+S:(_=xb(S.substr(2),b),S=pt(k)?k:(pe?Ne.utils.mapRange(0,pe.duration(),pe.scrollTrigger.start,pe.scrollTrigger.end,B):B)+_,C=oe)),S=ya(S,"end",Ce),Y=Math.max(B,fc(S||(C?"100% 0":y),C,b,ge,Ae()+_,q,I,Ce,m,Se,ye,y,pe,Ce._endClamp&&"_endClamp"))||-.001,_=0,P=E;P--;)(u=(c=Et[P]).pin)&&c.start-c._pinPush<=B&&!pe&&0<c.end&&(i=c.end-(Ce._startClamp?Math.max(0,c.start):c.start),(u===oe&&c.start-c._pinPush<B||u===T)&&isNaN(k)&&(_+=i*(1-c.progress)),u===ae&&(w+=i));if(B+=_,Y+=_,Ce._startClamp&&(Ce._startClamp+=_),Ce._endClamp&&!ot&&(Ce._endClamp=Y||-.001,Y=Math.min(Y,La(be,ge))),N=Y-B||(B-=.01)&&.001,x&&(Oe=Ne.utils.clamp(0,1,Ne.utils.normalize(B,Y,re))),Ce._pinPush=w,F&&_&&((i={})[ge.a]="+="+_,T&&(i[ge.p]="-="+Ae()),Ne.set([F,q],i)),ae)i=hb(ae),s=ge===Ie,a=Ae(),U=parseFloat(V(ge.a))+w,!y&&1<Y&&(h={style:h=(me?Ge.scrollingElement||je:be).style,value:h["overflow"+ge.a.toUpperCase()]},me&&"scroll"!==hb(Je)["overflow"+ge.a.toUpperCase()]&&(h.style["overflow"+ge.a.toUpperCase()]="scroll")),_b(ae,j,i),G=cc(ae),o=Ct(ae,!0),f=ye&&K(be,s?Le:Ie)(),se&&((Z=[se+ge.os2,N+w+St]).t=j,(P=se===xt?lb(ae,ge)+N+w:0)&&Z.push(ge.d,P+St),Dt(Z),T&&Et.forEach(function(e){e.pin===T&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),ye&&Ae(re)),ye&&((l={top:o.top+(s?a-B:f)+St,left:o.left+(s?f:a-B)+St,boxSizing:"border-box",position:"fixed"})[ht]=l.maxWidth=Math.ceil(o.width)+St,l[gt]=l.maxHeight=Math.ceil(o.height)+St,l[_t]=l[_t+mt]=l[_t+vt]=l[_t+yt]=l[_t+bt]="0",l[xt]=i[xt],l[xt+mt]=i[xt+mt],l[xt+vt]=i[xt+vt],l[xt+yt]=i[xt+yt],l[xt+bt]=i[xt+bt],W=function _copyState(e,t,r){for(var n,i=[],o=e.length,a=r?8:0;a<o;a+=2)n=e[a],i.push(n,n in t?t[n]:e[a+1]);return i.t=e.t,i}(H,l,fe),ot&&Ae(0)),O?(p=O._initted,nt(1),O.render(O.duration(),!0,!0),Q=V(ge.a)-U+N+w,$=1<Math.abs(N-Q),ye&&$&&W.splice(W.length-2,2),O.render(0,!0,!0),p||O.invalidate(!0),O.parent||O.totalTime(O.totalTime()),nt(0)):Q=N,h&&(h.value?h.style["overflow"+ge.a.toUpperCase()]=h.value:h.style.removeProperty("overflow-"+ge.a));else if(oe&&Ae()&&!pe)for(o=oe.parentNode;o&&o!==Je;)o._pinOffset&&(B-=o._pinOffset,Y-=o._pinOffset),o=o.parentNode;d&&d.forEach(function(e){return e.revert(!1,!0)}),Ce.start=B,Ce.end=Y,D=R=ot?re:Ae(),pe||ot||(D<re&&Ae(re),Ce.scroll.rec=0),Ce.revert(!1,!0),Me=ct(),te&&(Pe=-1,Ce.isActive&&Ae(B+N*Oe),te.restart(!0)),Qe=0,O&&ve&&(O._initted||ne)&&O.progress()!==ne&&O.progress(ne||0,!0).render(O.time(),!0,!0),(x||Oe!==Ce.progress||pe)&&(O&&!ve&&O.totalProgress(pe&&B<-.001&&!Oe?Ne.utils.normalize(B,Y,0):Oe,!0),Ce.progress=x||(D-B)/N===Oe?0:Oe),ae&&se&&(j._pinOffset=Math.round(Ce.progress*Q)),ee&&ee.invalidate(),isNaN(g)||(g-=Ne.getProperty(L,ge.p),v-=Ne.getProperty(I,ge.p),jc(L,ge,g),jc(F,ge,g-(n||0)),jc(I,ge,v),jc(q,ge,v-(n||0))),x&&!ot&&Ce.update(),!ie||ot||X||(X=!0,ie(Ce),X=!1)}},Ce.getVelocity=function(){return(Ae()-R)/(ct()-Ke)*1e3||0},Ce.endAnimation=function(){Ra(Ce.callbackAnimation),O&&(ee?ee.progress(1):O.paused()?ve||Ra(O,Ce.direction<0,1):Ra(O,O.reversed()))},Ce.labelToScroll=function(e){return O&&O.labels&&(B||Ce.refresh()||B)+O.labels[e]/O.duration()*N||0},Ce.getTrailing=function(t){var e=Et.indexOf(Ce),r=0<Ce.direction?Et.slice(0,e).reverse():Et.slice(e+1);return(pt(t)?r.filter(function(e){return e.vars.preventOverlaps===t}):r).filter(function(e){return 0<Ce.direction?e.end<=B:e.start>=Y})},Ce.update=function(e,t,r){if(!pe||r||e){var n,i,o,a,s,l,c,u=!0===ot?re:Ce.scroll(),f=e?0:(u-B)/N,p=f<0?0:1<f?1:f||0,d=Ce.progress;if(t&&(R=D,D=pe?Ae():u,ue&&(w=_,_=O&&!ve?O.totalProgress():p)),P&&!p&&ae&&!Qe&&!lt&&ut&&B<u+(u-R)/(ct()-Ke)*P&&(p=1e-4),p!==d&&Ce.enabled){if(a=(s=(n=Ce.isActive=!!p&&p<1)!=(!!d&&d<1))||!!p!=!!d,Ce.direction=d<p?1:-1,Ce.progress=p,a&&!Qe&&(i=p&&!d?0:1===p?1:1===d?2:3,ve&&(o=!s&&"none"!==_e[i+1]&&_e[i+1]||_e[i],c=O&&("complete"===o||"reset"===o||o in O))),he&&(s||c)&&(c||E||!O)&&(Oa(he)?he(Ce):Ce.getTrailing(he).forEach(function(e){return e.endAnimation()})),ve||(!ee||Qe||lt?O&&O.totalProgress(p,!(!Qe||!Me&&!e)):(ee._dp._time-ee._start!==ee._time&&ee.render(ee._dp._time-ee._start),ee.resetTo?ee.resetTo("totalProgress",p,O._tTime/O._tDur):(ee.vars.totalProgress=p,ee.invalidate().restart()))),ae)if(e&&se&&(j.style[se+ge.os2]=m),ye){if(a){if(l=!e&&d<p&&u<Y+1&&u+1>=La(be,ge),fe)if(e||!n&&!l)hc(ae,j);else{var h=Ct(ae,!0),g=u-B;hc(ae,Je,h.top+(ge===Ie?g:0)+St,h.left+(ge===Ie?0:g)+St)}Dt(n||l?W:G),$&&p<1&&n||b(U+(1!==p||l?0:Q))}}else b(Ea(U+Q*p));!ue||A.tween||Qe||lt||te.restart(!0),k&&(s||ce&&p&&(p<1||!it))&&Ve(k.targets).forEach(function(e){return e.classList[n||ce?"add":"remove"](k.className)}),!C||ve||e||C(Ce),a&&!Qe?(ve&&(c&&("complete"===o?O.pause().totalProgress(1):"reset"===o?O.restart(!0).pause():"restart"===o?O.restart(!0):O[o]()),C&&C(Ce)),!s&&it||(T&&s&&Sa(Ce,T),xe[i]&&Sa(Ce,xe[i]),ce&&(1===p?Ce.kill(!1,1):xe[i]=0),s||xe[i=1===p?1:3]&&Sa(Ce,xe[i])),de&&!n&&Math.abs(Ce.getVelocity())>(Pa(de)?de:2500)&&(Ra(Ce.callbackAnimation),ee?ee.progress(1):Ra(O,"reverse"===o?1:!p,1))):ve&&C&&!Qe&&C(Ce)}if(x){var v=pe?u/pe.duration()*(pe._caScrollDist||0):u;y(v+(L._isFlipped?1:0)),x(v)}S&&S(-u/pe.duration()*(pe._caScrollDist||0))}},Ce.enable=function(e,t){Ce.enabled||(Ce.enabled=!0,rb(be,"resize",Gb),rb(me?Ge:be,"scroll",Eb),ke&&rb(ScrollTrigger,"refreshInit",ke),!1!==e&&(Ce.progress=Oe=0,D=R=Pe=Ae()),!1!==t&&Ce.refresh())},Ce.getTween=function(e){return e&&A?A.tween:ee},Ce.setPositions=function(e,t,r,n){Ce.refresh(!1,!1,{start:za(e,r&&!!Ce._startClamp),end:za(t,r&&!!Ce._endClamp)},n),Ce.update()},Ce.adjustPinSpacing=function(e){if(Z&&e){var t=Z.indexOf(ge.d)+1;Z[t]=parseFloat(Z[t])+e+St,Z[1]=parseFloat(Z[1])+e+St,Dt(Z)}},Ce.disable=function(e,t){if(Ce.enabled&&(!1!==e&&Ce.revert(!0,!0),Ce.enabled=Ce.isActive=!1,t||ee&&ee.pause(),re=0,n&&(n.uncache=1),ke&&sb(ScrollTrigger,"refreshInit",ke),te&&(te.pause(),A.tween&&A.tween.kill()&&(A.tween=0)),!me)){for(var r=Et.length;r--;)if(Et[r].scroller===be&&Et[r]!==Ce)return;sb(be,"resize",Gb),sb(be,"scroll",Eb)}},Ce.kill=function(e,t){Ce.disable(e,t),ee&&!t&&ee.kill(),a&&delete Pt[a];var r=Et.indexOf(Ce);0<=r&&Et.splice(r,1),r===tt&&0<At&&tt--,r=0,Et.forEach(function(e){return e.scroller===Ce.scroller&&(r=1)}),r||ot||(Ce.scroll.rec=0),O&&(O.scrollTrigger=null,e&&O.revert({kill:!1}),t||O.kill()),F&&[F,q,L,I].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),st===Ce&&(st=0),ae&&(n&&(n.uncache=1),r=0,Et.forEach(function(e){return e.pin===ae&&r++}),r||(n.spacer=0)),M.onKill&&M.onKill(Ce)},Et.push(Ce),Ce.enable(!1,!1),o&&o(Ce),O&&O.add&&!N){var v=Ce.update;Ce.update=function(){Ce.update=v,B||Y||Ce.refresh()},Ne.delayedCall(.01,Ce.update),N=.01,B=Y=0}else Ce.refresh();ae&&function _queueRefreshAll(){if(at!==Mt){var e=at=Mt;requestAnimationFrame(function(){return e===Mt&&Ot(!0)})}}()}else this.update=this.refresh=this.kill=Da},ScrollTrigger.register=function register(e){return s||(Ne=e||Ga(),Fa()&&window.document&&ScrollTrigger.enable(),s=ft),s},ScrollTrigger.defaults=function defaults(e){if(e)for(var t in e)Tt[t]=e[t];return Tt},ScrollTrigger.disable=function disable(t,r){ft=0,Et.forEach(function(e){return e[r?"kill":"disable"](t)}),sb(He,"wheel",Eb),sb(Ge,"scroll",Eb),clearInterval(u),sb(Ge,"touchcancel",Da),sb(Je,"touchstart",Da),qb(sb,Ge,"pointerdown,touchstart,mousedown",Ba),qb(sb,Ge,"pointerup,touchend,mouseup",Ca),c.kill(),Ma(sb);for(var e=0;e<ze.length;e+=3)tb(sb,ze[e],ze[e+1]),tb(sb,ze[e],ze[e+2])},ScrollTrigger.enable=function enable(){if(He=window,Ge=document,je=Ge.documentElement,Je=Ge.body,Ne&&(Ve=Ne.utils.toArray,Ue=Ne.utils.clamp,x=Ne.core.context||Da,nt=Ne.core.suppressOverwrites||Da,_=He.history.scrollRestoration||"auto",j=He.pageYOffset,Ne.core.globals("ScrollTrigger",ScrollTrigger),Je)){ft=1,function _rafBugFix(){return ft&&requestAnimationFrame(_rafBugFix)}(),T.register(Ne),ScrollTrigger.isTouch=T.isTouch,E=T.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),rb(He,"wheel",Eb),l=[He,Ge,je,Je],Ne.matchMedia?(ScrollTrigger.matchMedia=function(e){var t,r=Ne.matchMedia();for(t in e)r.add(t,e[t]);return r},Ne.addEventListener("matchMediaInit",function(){return Nb()}),Ne.addEventListener("matchMediaRevert",function(){return Mb()}),Ne.addEventListener("matchMedia",function(){Ot(0,1),H("matchMedia")}),Ne.matchMedia("(orientation: portrait)",function(){return Fb(),Fb})):console.warn("Requires GSAP 3.11.0 or later"),Fb(),rb(Ge,"scroll",Eb);var e,t,r=Je.style,n=r.borderTopStyle,i=Ne.core.Animation.prototype;for(i.revert||Object.defineProperty(i,"revert",{value:function value(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",e=Ct(Je),Ie.m=Math.round(e.top+Ie.sc())||0,Le.m=Math.round(e.left+Le.sc())||0,n?r.borderTopStyle=n:r.removeProperty("border-top-style"),u=setInterval(Db,250),Ne.delayedCall(.5,function(){return lt=0}),rb(Ge,"touchcancel",Da),rb(Je,"touchstart",Da),qb(rb,Ge,"pointerdown,touchstart,mousedown",Ba),qb(rb,Ge,"pointerup,touchend,mouseup",Ca),f=Ne.utils.checkPrefix("transform"),Q.push(f),s=ct(),c=Ne.delayedCall(.2,Ot).pause(),h=[Ge,"visibilitychange",function(){var e=He.innerWidth,t=He.innerHeight;Ge.hidden?(p=e,d=t):p===e&&d===t||Gb()},Ge,"DOMContentLoaded",Ot,He,"load",Ot,He,"resize",Gb],Ma(rb),Et.forEach(function(e){return e.enable(0,1)}),t=0;t<ze.length;t+=3)tb(sb,ze[t],ze[t+1]),tb(sb,ze[t],ze[t+2])}},ScrollTrigger.config=function config(e){"limitCallbacks"in e&&(it=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(u)||(u=t)&&setInterval(Db,t),"ignoreMobileResize"in e&&(b=1===ScrollTrigger.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(Ma(sb)||Ma(rb,e.autoRefreshEvents||"none"),g=-1===(e.autoRefreshEvents+"").indexOf("resize"))},ScrollTrigger.scrollerProxy=function scrollerProxy(e,t){var r=J(e),n=ze.indexOf(r),i=Ha(r);~n&&ze.splice(n,i?6:2),t&&(i?Fe.unshift(He,t,Je,t,je,t):Fe.unshift(r,t))},ScrollTrigger.clearMatchMedia=function clearMatchMedia(t){Et.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},ScrollTrigger.isInViewport=function isInViewport(e,t,r){var n=(pt(e)?J(e):e).getBoundingClientRect(),i=n[r?ht:gt]*t||0;return r?0<n.right-i&&n.left+i<He.innerWidth:0<n.bottom-i&&n.top+i<He.innerHeight},ScrollTrigger.positionInViewport=function positionInViewport(e,t,r){pt(e)&&(e=J(e));var n=e.getBoundingClientRect(),i=n[r?ht:gt],o=null==t?i/2:t in Y?Y[t]*i:~t.indexOf("%")?parseFloat(t)*i/100:parseFloat(t)||0;return r?(n.left+o)/He.innerWidth:(n.top+o)/He.innerHeight},ScrollTrigger.killAll=function killAll(e){if(Et.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=I.killAll||[];I={},t.forEach(function(e){return e()})}},ScrollTrigger);function ScrollTrigger(e,t){s||ScrollTrigger.register(Ne)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),x(this),this.init(e,t)}ee.version="3.12.0",ee.saveStyles=function(e){return e?Ve(e).forEach(function(e){if(e&&e.style){var t=W.indexOf(e);0<=t&&W.splice(t,5),W.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Ne.core.getCache(e),x())}}):W},ee.revert=function(e,t){return Nb(!e,t)},ee.create=function(e,t){return new ee(e,t)},ee.refresh=function(e){return e?Gb():(s||ee.register())&&Ot(!0)},ee.update=function(e){return++ze.cache&&V(!0===e?2:0)},ee.clearScrollMemory=Ob,ee.maxScroll=function(e,t){return La(e,t?Le:Ie)},ee.getScrollFunc=function(e,t){return K(J(e),t?Le:Ie)},ee.getById=function(e){return Pt[e]},ee.getAll=function(){return Et.filter(function(e){return"ScrollSmoother"!==e.vars.id})},ee.isScrolling=function(){return!!ut},ee.snapDirectional=ob,ee.addEventListener=function(e,t){var r=I[e]||(I[e]=[]);~r.indexOf(t)||r.push(t)},ee.removeEventListener=function(e,t){var r=I[e],n=r&&r.indexOf(t);0<=n&&r.splice(n,1)},ee.batch=function(e,t){function mp(e,t){var r=[],n=[],i=Ne.delayedCall(o,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||i.restart(!0),r.push(e.trigger),n.push(e),a<=r.length&&i.progress(1)}}var r,n=[],i={},o=t.interval||.016,a=t.batchMax||1e9;for(r in t)i[r]="on"===r.substr(0,2)&&Oa(t[r])&&"onRefreshInit"!==r?mp(0,t[r]):t[r];return Oa(a)&&(a=a(),rb(ee,"refresh",function(){return a=t.batchMax()})),Ve(e).forEach(function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,n.push(ee.create(t))}),n};function mc(e,t,r,n){return n<t?e(n):t<0&&e(0),n<r?(n-t)/(r-t):r<0?t/(t-r):1}function nc(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(T.isTouch?" pinch-zoom":""):"none",e===je&&nc(Je,t)}function pc(e){var t,r=e.event,n=e.target,i=e.axis,o=(r.changedTouches?r.changedTouches[0]:r).target,a=o._gsap||Ne.core.getCache(o),s=ct();if(!a._isScrollT||2e3<s-a._isScrollT){for(;o&&o!==Je&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!re[(t=hb(o)).overflowY]&&!re[t.overflowX]);)o=o.parentNode;a._isScroll=o&&o!==n&&!Ha(o)&&(re[(t=hb(o)).overflowY]||re[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==i||(r.stopPropagation(),r._gsapAllow=!0)}function qc(e,t,r,n){return T.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&pc,onPress:n,onDrag:n,onScroll:n,onEnable:function onEnable(){return r&&rb(Ge,T.eventTypes[0],ie,!1,!0)},onDisable:function onDisable(){return sb(Ge,T.eventTypes[0],ie,!0)}})}function uc(e){function jq(){return i=!1}function mq(){o=La(d,Ie),k=Ue(E?1:0,o),f&&(C=Ue(0,La(d,Le))),l=Mt}function nq(){v._gsap.y=Ea(parseFloat(v._gsap.y)+b.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",b.offset=b.cacheID=0}function tq(){mq(),a.isActive()&&a.vars.scrollY>o&&(b()>o?a.progress(1)&&b(o):a.resetTo("scrollY",o))}Qa(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var n,o,l,i,a,c,u,s,f=e.normalizeScrollX,t=e.momentum,r=e.allowNestedScroll,p=e.onRelease,d=J(e.target)||je,h=Ne.core.globals().ScrollSmoother,g=h&&h.get(),v=E&&(e.content&&J(e.content)||g&&!1!==e.content&&!g.smooth()&&g.content()),b=K(d,Ie),m=K(d,Le),y=1,x=(T.isTouch&&He.visualViewport?He.visualViewport.scale*He.visualViewport.width:He.outerWidth)/He.innerWidth,_=0,w=Oa(t)?function(){return t(n)}:function(){return t||2.8},S=qc(d,e.type,!0,r),C=Da,k=Da;return v&&Ne.set(v,{y:"+=0"}),e.ignoreCheck=function(e){return E&&"touchmove"===e.type&&function ignoreDrag(){if(i){requestAnimationFrame(jq);var e=Ea(n.deltaY/2),t=k(b.v-e);if(v&&t!==b.v+b.offset){b.offset=t-b.v;var r=Ea((parseFloat(v&&v._gsap.y)||0)-b.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+r+", 0, 1)",v._gsap.y=r+"px",b.cacheID=ze.cache,V()}return!0}b.offset&&nq(),i=!0}()||1.05<y&&"touchstart"!==e.type||n.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){i=!1;var e=y;y=Ea((He.visualViewport&&He.visualViewport.scale||1)/x),a.pause(),e!==y&&nc(d,1.01<y||!f&&"x"),c=m(),u=b(),mq(),l=Mt},e.onRelease=e.onGestureStart=function(e,t){if(b.offset&&nq(),t){ze.cache++;var r,n,i=w();f&&(n=(r=m())+.05*i*-e.velocityX/.227,i*=mc(m,r,n,La(d,Le)),a.vars.scrollX=C(n)),n=(r=b())+.05*i*-e.velocityY/.227,i*=mc(b,r,n,La(d,Ie)),a.vars.scrollY=k(n),a.invalidate().duration(i).play(.01),(E&&a.vars.scrollY>=o||o-1<=r)&&Ne.to({},{onUpdate:tq,duration:i})}else s.restart(!0);p&&p(e)},e.onWheel=function(){a._ts&&a.pause(),1e3<ct()-_&&(l=0,_=ct())},e.onChange=function(e,t,r,n,i){if(Mt!==l&&mq(),t&&f&&m(C(n[2]===t?c+(e.startX-e.x):m()+t-n[1])),r){b.offset&&nq();var o=i[2]===r,a=o?u+e.startY-e.y:b()+r-i[1],s=k(a);o&&a!==s&&(u+=s-a),b(s)}(r||t)&&V()},e.onEnable=function(){nc(d,!f&&"x"),ee.addEventListener("refresh",tq),rb(He,"resize",tq),b.smooth&&(b.target.style.scrollBehavior="auto",b.smooth=m.smooth=!1),S.enable()},e.onDisable=function(){nc(d,!0),sb(He,"resize",tq),ee.removeEventListener("refresh",tq),S.kill()},e.lockAxis=!1!==e.lockAxis,((n=new T(e)).iOS=E)&&!b()&&b(1),E&&Ne.ticker.add(Da),s=n._dc,a=Ne.to(n,{ease:"power4",paused:!0,scrollX:f?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:ic(b,b(),function(){return a.pause()})},onUpdate:V,onComplete:s.vars.onComplete}),n}var te,re={auto:1,scroll:1},ne=/(input|label|select|textarea)/i,ie=function _captureInputs(e){var t=ne.test(e.target.tagName);(t||te)&&(e._gsapAllow=!0,te=t)};ee.sort=function(e){return Et.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},ee.observe=function(e){return new T(e)},ee.normalizeScroll=function(e){if(void 0===e)return v;if(!0===e&&v)return v.enable();if(!1===e)return v&&v.kill();var t=e instanceof T?e:uc(e);return v&&v.target===t.target&&v.kill(),Ha(t.target)&&(v=t),t},ee.core={_getVelocityProp:L,_inputObserver:qc,_scrollers:ze,_proxies:Fe,bridge:{ss:function ss(){ut||H("scrollStart"),ut=ct()},ref:function ref(){return Qe}}},Ga()&&Ne.registerPlugin(ee),e.ScrollTrigger=ee,e.default=ee;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});
