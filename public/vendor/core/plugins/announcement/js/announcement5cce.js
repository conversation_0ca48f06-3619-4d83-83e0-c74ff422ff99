(()=>{"use strict";function n(n){return function(n){if(Array.isArray(n))return t(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||function(n,e){if(n){if("string"==typeof n)return t(n,e);var o={}.toString.call(n).slice(8,-1);return"Object"===o&&n.constructor&&(o=n.constructor.name),"Map"===o||"Set"===o?Array.from(n):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?t(n,e):void 0}}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,o=Array(t);e<t;e++)o[e]=n[e];return o}document.addEventListener("DOMContentLoaded",(function(){var t=function(){var t=document.querySelector(".ae-anno-announcement-wrapper");if(t){var e=t.querySelectorAll(".ae-anno-announcement"),o=document.querySelector(".ae-anno-announcement__next-button"),r=document.querySelector(".ae-anno-announcement__previous-button"),a=document.querySelector(".ae-anno-announcement__dismiss-button"),c=null!==t.getAttribute("data-announcement-autoplay"),i=parseInt(t.getAttribute("data-announcement-autoplay-delay")||5e3),u=JSON.parse(function(n){for(var t=n+"=",e=document.cookie.split(";"),o=0;o<e.length;o++){for(var r=e[o];" "===r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return r.substring(t.length,r.length)}return null}("ae-anno-dismissed-announcements")||"[]"),l=1,s=null,d=function(){c&&i&&(s&&clearInterval(s),s=setInterval((function(){l++,f(l)}),i))},f=function(){l>e.length?l=1:l<1&&(l=e.length),e.forEach((function(n){n.style.display="none"})),e[l-1].style.display="block",d()};f(l),o&&o.addEventListener("click",(function(){l++,f(l)})),r&&r.addEventListener("click",(function(){f(l--)})),a&&a.addEventListener("click",(function(){var e=JSON.parse(a.getAttribute("data-announcement-ids"));u.push.apply(u,n(e)),function(n,t,e){var o="";if(e){var r=new Date;r.setTime(r.getTime()+24*e*60*60*1e3),o="; expires=".concat(r.toUTCString())}var a="https:"===window.location.protocol?"; Secure":"";document.cookie="".concat(n,"=").concat(t||"").concat(o,"; path=/; SameSite=Lax").concat(a)}("ae-anno-dismissed-announcements",JSON.stringify(u),365),t.parentNode.removeChild(t)})),d()}},e=$('[data-bb-toggle="announcement-lazy-loading"]');e.length?fetch(e.data("url"),{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}}).then((function(n){if(!n.ok)throw new Error("Network response was not ok");return n.json()})).then((function(n){var o=n.data;e.replaceWith(o),t()})).catch((function(n){console.error("Fetch error:",n)})):t()}))})();