<!DOCTYPE html>
<html data-bs-theme="dark" lang="en">

<meta http-equiv="content-type" content="text/html;charset=UTF-8" />

@include('layout.includes.head')

<body id="page-home" class="home-page-1">
   @include('layout.includes.top-nav')
    <div id="preloader">
        <div class="loader-cover">
            <div class="loader-container">
                <div class="loader-icon"></div>
            </div>
        </div>
    </div>
    @yield('content')
    <footer>
        <div class="section-footer position-relative pt-60 pb-60 bg-secondary-1">
            <div class="text-center container position-relative z-1"><a
                    class="d-flex main-logo align-items-center d-inline-flex" href="index.html"><img
                        src=storage/main/general/logo-dark.png data-bb-lazy="false" class="page_speed_353846434"
                        alt="Showcasing Creative Designs and Innovative Projects"><span
                        class="fs-4 ms-2 site-name-text text-white-keep"><PERSON></span></a>
                <div
                    class="navigation align-items-center justify-content-center flex-wrap gap-4 my-4 d-none d-md-flex">
                    <a href="index.html" class="fs-5"> Home </a><a href="services.html" class="fs-5"> Services
                    </a><a href="pricing.html" class="fs-5"> Pricing </a><a href="blog.html" class="fs-5"> Blog
                    </a><a href="contact.html" class="fs-5"> Contact </a>
                </div>
                <div class="row text-center py-4"><span class="fs-6 text-white-keep"> © 2025 All Rights Reserved by <a
                            href="#" class="text-primary">John Muthee</a>. </span></div>
            </div>
            <div class="position-absolute top-0 start-0 w-100 h-100 z-0"
                data-background="https://zelio.botble.com/storage/main/general/footer-bg.png"></div>
        </div>
    </footer>
    <div class="btn-scroll-top style-btn-2"><svg class="progress-square svg-content" width=100% height=100%
            viewBox="0 0 40 40">
            <path
                d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
        </svg></div>
    <!-- Cloudflare email decode script removed to prevent ad blocker conflicts -->
    <script data-pagespeed-no-defer="1" src=themes/zelio/js/vendors/jquery-3.7.1.min.js></script>
    <script src=themes/zelio/js/vendors/bootstrap.bundle.min.js></script>
    <script src=themes/zelio/js/vendors/swiper-bundle.min.js></script>
    <script src=themes/zelio/js/vendors/aos.js></script>
    <script src=themes/zelio/js/vendors/wow.min.js></script>
    <script src=themes/zelio/js/vendors/headhesive.min.js></script>
    <script src=themes/zelio/js/vendors/smart-stick-nav.js></script>
    <script src=themes/zelio/js/vendors/jquery.magnific-popup.min.js></script>
    <script src=themes/zelio/js/vendors/gsap.min.js></script>
    <script src=themes/zelio/js/vendors/imagesloaded.pkgd.min.js></script>
    <script src=themes/zelio/js/vendors/isotope.pkgd.min.js></script>
    <script src=themes/zelio/js/vendors/ScrollToPlugin.min.js></script>
    <script src=themes/zelio/js/vendors/ScrollTrigger.min.js></script>
    <script src=themes/zelio/js/vendors/jquery.carouselTicker.min.js></script>
    <script src=themes/zelio/js/vendors/slick.min.js></script>
    <script src=themes/zelio/js/vendors/jquery.appear38bb.js?v=*******></script>
    <script src=themes/zelio/js/vendors/gsap-custom38bb.js?v=*******></script>
    <script src=themes/zelio/js/imageRevealHover38bb.js?v=*******></script>
    <script src=themes/zelio/js/vendors/aat.min.js></script>
    <script src=themes/zelio/js/vendors/color-modes38bb.js?v=*******></script>
    <script src=themes/zelio/js/main38bb.js?v=*******></script>
    <script src=themes/zelio/js/theme38bb.js?v=*******></script>
    <script src=vendor/core/plugins/language/js/language-publicd1f1.js?v=2.2.0></script>
    <script async defer src='https://www.googletagmanager.com/gtag/js?id=G-76NX8HY29D'></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'G-76NX8HY29D');
    </script>
    <script src=vendor/core/plugins/announcement/js/announcement5cce.js?v=1.1.7></script>
    <script>
        // Replace lazy loading with placeholder content to prevent fetch errors
        var hideLazyLoadingSpinners = function() {
            document.querySelectorAll('.shortcode-lazy-loading').forEach(function(element) {
                var name = element.getAttribute('data-name');

                // Create a placeholder message
                var placeholder = document.createElement('div');
                placeholder.className = 'text-center py-5 text-muted';
                placeholder.innerHTML = '<p>Content section: ' + name + '</p><p><small>This section would normally load dynamic content.</small></p>';

                // Replace the loading spinner with the placeholder
                element.innerHTML = '';
                element.appendChild(placeholder);
                element.style.minHeight = 'auto';
            });
        };

        window.addEventListener('load', function() {
            hideLazyLoadingSpinners();
        });
    </script>
    <style>
        .site-notice {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 8px;
            z-index: 99999;
            display: none;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .site-notice.site-notice--visible {
            display: block;
        }

        .site-notice.site-notice-full-width .site-notice-body {
            margin: 0 auto;
        }

        .site-notice.site-notice-minimal {
            padding: 0;
            right: unset;
            border-radius: 5px;
            bottom: 1em;
            flex-direction: column;
            left: 1em;
        }

        .site-notice.site-notice-minimal .site-notice-body {
            margin: 0 16px 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 400px !important;
        }

        .site-notice.site-notice-minimal .site-notice__inner {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
            padding: 4px;
        }

        .site-notice.site-notice-minimal .site-notice__message {
            font-size: 13px;
            line-height: 1.5;
            padding: 12px 12px 0;
        }

        .site-notice.site-notice-minimal .site-notice__actions {
            margin: 0;
            padding: 8px 12px 12px;
            justify-content: flex-end;
            gap: 8px;
        }

        .site-notice.site-notice-minimal .site-notice__actions button {
            min-width: auto;
            padding: 6px 12px;
            font-size: 12px;
        }

        .site-notice.site-notice-minimal .site-notice__categories {
            padding: 12px;
            margin-top: 0;
        }

        .site-notice.site-notice-minimal .site-notice__categories .cookie-category {
            padding: 8px;
            margin-bottom: 8px;
        }

        .site-notice.site-notice-minimal .site-notice__categories .cookie-category:last-child {
            margin-bottom: 0;
        }

        .site-notice.site-notice-minimal .site-notice__categories .cookie-category__description {
            font-size: 12px;
        }

        .site-notice.site-notice-minimal .site-notice__categories .cookie-consent__save {
            padding: 8px 0 0;
            margin-top: 8px;
        }

        .site-notice.site-notice-minimal .site-notice__categories .cookie-consent__save .cookie-consent__save-button {
            font-size: 12px;
            padding: 6px 12px;
        }

        .site-notice .site-notice-body {
            padding: 8px 15px;
            border-radius: 4px;
        }

        .site-notice .site-notice__inner {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .site-notice .site-notice__message {
            margin: 0;
            line-height: 1.4;
            font-size: 14px;
            flex: 1;
            min-width: 200px;
        }

        .site-notice .site-notice__message a {
            color: inherit;
            text-decoration: underline;
        }

        .site-notice .site-notice__message a:hover {
            text-decoration: none;
        }

        .site-notice .site-notice__categories {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .site-notice .site-notice__categories .cookie-category {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .site-notice .site-notice__categories .cookie-category__label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .site-notice .site-notice__categories .cookie-category__label input[type="checkbox"] {
            margin: 0;
            padding: 0;
            border: none;
            border-radius: 0;
            box-shadow: none;
            font-size: initial;
            height: initial;
            width: auto;
        }

        .site-notice .site-notice__categories .cookie-category__label input[type="checkbox"]:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .site-notice .site-notice__categories .cookie-category__name {
            font-weight: bold;
        }

        .site-notice .site-notice__categories .cookie-category__description {
            margin: 0;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .site-notice .site-notice__categories .cookie-consent__save {
            margin-top: 1rem;
            padding: .75rem;
        }

        [dir="rtl"] .site-notice .site-notice__categories .cookie-consent__save {
            text-align: left;
        }

        .site-notice .site-notice__categories .cookie-consent__save .cookie-consent__save-button {
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            min-width: 100px;
            text-align: center;
            font-weight: bold;
        }

        .site-notice .site-notice__categories .cookie-consent__save .cookie-consent__save-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .site-notice .site-notice__categories .cookie-consent__save .cookie-consent__save-button:active {
            transform: translateY(0);
        }

        .site-notice .site-notice__actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: flex-end;
            margin-left: auto;
        }

        [dir="rtl"] .site-notice .site-notice__actions {
            margin-left: 0;
            margin-right: auto;
        }

        .site-notice .site-notice__actions button {
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            min-width: 100px;
            text-align: center;
        }

        .site-notice .site-notice__actions button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .site-notice .site-notice__actions button:active {
            transform: translateY(0);
        }

        .site-notice .cookie-consent__actions .site-notice__reject {
            font-weight: 500;
            opacity: 0.95;
        }

        .site-notice .cookie-consent__actions .site-notice__reject:hover {
            opacity: 1;
        }

        .site-notice .cookie-consent__actions .site-notice__customize {
            font-weight: 500;
            opacity: 0.95;
        }

        .site-notice .cookie-consent__actions .site-notice__customize:hover {
            opacity: 1;
        }

        .site-notice .cookie-consent__actions .site-notice__customize.active {
            opacity: 0.8;
            transform: translateY(0);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .site-notice .cookie-consent__actions .site-notice__agree {
            font-weight: bold;
            position: relative;
        }

        .site-notice .cookie-consent__actions .site-notice__agree:before {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            z-index: -1;
        }

        @media (max-width: 767px) {
            .site-notice .site-notice__inner {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }

            [dir="rtl"] .site-notice .site-notice__inner {
                flex-direction: column;
            }

            .site-notice .site-notice__actions {
                justify-content: center;
                margin-left: 0;
                gap: 0.4rem;
                flex-wrap: wrap;
            }

            [dir="rtl"] .site-notice .site-notice__actions {
                margin-right: 0;
            }

            .site-notice .site-notice__actions button {
                flex: none;
                min-width: 70px;
                max-width: 100px;
                padding: 6px 10px;
                font-size: 11px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                border-radius: 3px;
            }
        }
    </style>
    <div dir="ltr" data-nosnippet class="js-site-notice site-notice site-notice-full-width page_speed_590282898">
        <div class="site-notice-body page_speed_1747309764">
            <div class="site-notice__inner">
                <div class="site-notice__message"> Your experience on this site will be improved by allowing cookies.
                    <a href="cookie-policy.html">Cookie Policy</a></div>
                <div class="site-notice__actions"><button
                        class="js-site-notice-reject site-notice__reject page_speed_1046531075"> Reject
                    </button><button class="js-site-notice-customize site-notice__customize page_speed_1046531075">
                        Customize
                        preferences </button><button
                        class="js-site-notice-agree site-notice__agree page_speed_1003585387"> Allow cookies </button>
                </div>
            </div>
            <div class="site-notice__categories">
                <div class="cookie-category"><label class="cookie-category__label"><input type=checkbox
                            name=cookie_category[] value="essential" class="js-cookie-category" checked disabled><span
                            class="cookie-category__name">Essential</span></label>
                    <p class="cookie-category__description">These cookies are essential for the website to function
                        properly.</p>
                </div>
                <div class="cookie-category"><label class="cookie-category__label"><input type=checkbox
                            name=cookie_category[] value="analytics" class="js-cookie-category"><span
                            class="cookie-category__name">Analytics</span></label>
                    <p class="cookie-category__description">These cookies help us understand how visitors interact with
                        the website.</p>
                </div>
                <div class="cookie-category"><label class="cookie-category__label"><input type=checkbox
                            name=cookie_category[] value="marketing" class="js-cookie-category"><span
                            class="cookie-category__name">Marketing</span></label>
                    <p class="cookie-category__description">These cookies are used to deliver personalized
                        advertisements.</p>
                </div>
                <div class="site-notice__save"><button
                        class="js-site-notice-save site-notice__save-button page_speed_1003585387"> Save preferences
                    </button></div>
            </div>
        </div>
    </div>
    <div data-site-cookie-name="cookie_for_consent"></div>
    <div data-site-cookie-lifetime="7300"></div>
    <div data-site-cookie-domain="zelio.botble.com"></div>
    <div data-site-session-secure=""></div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function checkCookie(name) {
                return document.cookie.split(';').some((item) => item.trim().startsWith(name + '='));
            }
            setTimeout(function() {
                const cookieName = document.querySelector('div[data-site-cookie-name]').getAttribute(
                    'data-site-cookie-name') || 'cookie_for_consent';
                if (!checkCookie(cookieName)) {
                    const siteNotice = document.querySelector('.js-site-notice');
                    if (siteNotice) {
                        siteNotice.classList.add('site-notice--visible');
                    }
                }
            }, 1000);
        });
        window.addEventListener('load', function() {
            if (typeof gtag !== 'undefined') {
                gtag('consent', 'default', {
                    'ad_storage': 'denied',
                    'analytics_storage': 'denied'
                });
                document.addEventListener('click', function(event) {
                    if (event.target.classList.contains('js-site-notice-agree')) {
                        const categories = document.querySelectorAll('.js-cookie-category:checked');
                        const consents = {
                            'ad_storage': 'denied',
                            'analytics_storage': 'denied'
                        };
                        categories.forEach(function(category) {
                            if (category.value === 'marketing') {
                                consents.ad_storage = 'granted';
                            }
                            if (category.value === 'analytics') {
                                consents.analytics_storage = 'granted';
                            }
                        });
                        gtag('consent', 'update', consents);
                    }
                });
            }
            window.botbleCookieConsent = (function() {
                const COOKIE_NAME = document.querySelector('div[data-site-cookie-name]').getAttribute(
                    'data-site-cookie-name') || 'cookie_for_consent';
                const COOKIE_DOMAIN = document.querySelector('div[data-site-cookie-domain]').getAttribute(
                    'data-site-cookie-domain') || window.location.hostname;
                const COOKIE_LIFETIME = parseInt(document.querySelector('div[data-site-cookie-lifetime]')
                    .getAttribute('data-site-cookie-lifetime') || '36000', 10);
                const SESSION_SECURE = document.querySelector('div[data-site-session-secure]').getAttribute(
                    'data-site-session-secure') || '';
                const cookieDialog = document.querySelector('.js-site-notice');
                const cookieCategories = document.querySelector('.site-notice__categories');
                const customizeButton = document.querySelector('.js-site-notice-customize');
                if (cookieDialog) {
                    if (cookieCategories) {
                        cookieCategories.style.display = 'none';
                    }
                    if (!cookieExists(COOKIE_NAME)) {
                        setTimeout(function() {
                            cookieDialog.classList.add('site-notice--visible');
                        }, 800);
                    }
                }

                function consentWithCookies() {
                    const categories = {};
                    document.querySelectorAll('.js-cookie-category:checked').forEach(function(checkbox) {
                        categories[checkbox.value] = true;
                    });
                    setCookie(COOKIE_NAME, JSON.stringify(categories), COOKIE_LIFETIME);
                    hideCookieDialog();
                }

                function savePreferences() {
                    consentWithCookies();
                    if (cookieCategories) {
                        const slideUpAnimation = cookieCategories.animate([{
                            opacity: 1,
                            height: cookieCategories.offsetHeight + 'px'
                        }, {
                            opacity: 0,
                            height: 0
                        }], {
                            duration: 300,
                            easing: 'ease-out'
                        });
                        slideUpAnimation.onfinish = function() {
                            cookieCategories.style.display = 'none';
                        };
                    }
                    if (customizeButton) {
                        customizeButton.classList.remove('active');
                    }
                }

                function rejectAllCookies() {
                    if (cookieExists(COOKIE_NAME)) {
                        const secure = window.location.protocol === 'https:' ? '; Secure' : '';
                        document.cookie = COOKIE_NAME +
                            '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=' + COOKIE_DOMAIN +
                            '; path=/; SameSite=Lax' + secure + SESSION_SECURE;
                    }
                    if (typeof gtag !== 'undefined') {
                        gtag('consent', 'update', {
                            'ad_storage': 'denied',
                            'analytics_storage': 'denied'
                        });
                    }
                    hideCookieDialog();
                }

                function cookieExists(name) {
                    const cookie = getCookie(name);
                    return cookie !== null && cookie !== undefined;
                }

                function getCookie(name) {
                    const value = `; ${document.cookie}`;
                    const parts = value.split(`; ${name}=`);
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                    return null;
                }

                function hideCookieDialog() {
                    if (cookieDialog) {
                        cookieDialog.classList.remove('site-notice--visible');
                        cookieDialog.style.display = 'none';
                    }
                }

                function setCookie(name, value, expirationInDays) {
                    const date = new Date();
                    date.setTime(date.getTime() + expirationInDays * 24 * 60 * 60 * 1000);
                    const secure = window.location.protocol === 'https:' ? ';Secure' : '';
                    document.cookie = name + '=' + value + ';expires=' + date.toUTCString() + ';domain=' +
                        COOKIE_DOMAIN + ';path=/' + ';SameSite=Lax' + secure + SESSION_SECURE;
                }

                function toggleCustomizeView() {
                    if (!cookieCategories) return;
                    if (cookieCategories.style.display === 'none') {
                        cookieCategories.style.height = '0';
                        cookieCategories.style.opacity = '0';
                        cookieCategories.style.display = 'block';
                        const height = cookieCategories.scrollHeight;
                        const slideDownAnimation = cookieCategories.animate([{
                            opacity: 0,
                            height: 0
                        }, {
                            opacity: 1,
                            height: height + 'px'
                        }], {
                            duration: 300,
                            easing: 'ease-in'
                        });
                        slideDownAnimation.onfinish = function() {
                            cookieCategories.style.height = 'auto';
                            cookieCategories.style.opacity = '1';
                        };
                        if (customizeButton) {
                            customizeButton.classList.add('active');
                        }
                    } else {
                        const slideUpAnimation = cookieCategories.animate([{
                            opacity: 1,
                            height: cookieCategories.offsetHeight + 'px'
                        }, {
                            opacity: 0,
                            height: 0
                        }], {
                            duration: 300,
                            easing: 'ease-out'
                        });
                        slideUpAnimation.onfinish = function() {
                            cookieCategories.style.display = 'none';
                        };
                        if (customizeButton) {
                            customizeButton.classList.remove('active');
                        }
                    }
                }
                if (cookieExists(COOKIE_NAME)) {
                    hideCookieDialog();
                }
                document.addEventListener('click', function(event) {
                    if (event.target.classList.contains('js-site-notice-agree')) {
                        consentWithCookies();
                    } else if (event.target.classList.contains('js-site-notice-reject')) {
                        rejectAllCookies();
                    } else if (event.target.classList.contains('js-site-notice-customize')) {
                        toggleCustomizeView();
                    } else if (event.target.classList.contains('js-site-notice-save')) {
                        savePreferences();
                    }
                });
                return {
                    consentWithCookies: consentWithCookies,
                    rejectAllCookies: rejectAllCookies,
                    hideCookieDialog: hideCookieDialog,
                    savePreferences: savePreferences,
                };
            })();
        });
    </script>
    <!-- Cloudflare Insights script removed to prevent ad blocker conflicts -->
</body>


</html>
